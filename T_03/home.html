<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 医疗专家诊断</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            overflow-x: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: #4CAF50;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            border-radius: 0 0 25px 25px;
        }
        
        .user-greeting {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .user-info h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }
        
        .user-info p {
            margin: 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .search-box {
            background: rgba(255,255,255,0.15);
            border: none;
            border-radius: 15px;
            padding: 12px 20px;
            color: white;
            width: 100%;
            backdrop-filter: blur(10px);
        }
        
        .search-box::placeholder {
            color: rgba(255,255,255,0.8);
        }
        
        .quick-actions {
            padding: 20px;
            margin-top: -10px;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .action-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            color: inherit;
        }
        
        .action-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-size: 24px;
            color: white;
        }
        
        .action-icon.consult {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }
        
        .action-icon.appointment {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }
        
        .action-icon.health {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }
        
        .action-icon.emergency {
            background: linear-gradient(135deg, #F44336, #D32F2F);
        }
        
        .action-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        
        .action-subtitle {
            font-size: 12px;
            color: #666;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 8px;
            color: #4CAF50;
        }
        
        .expert-card {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
        }
        
        .expert-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-right: 15px;
        }
        
        .expert-info {
            flex: 1;
        }
        
        .expert-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }
        
        .expert-specialty {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .expert-rating {
            color: #4CAF50;
            font-size: 12px;
        }
        
        .expert-status {
            background: #e8f5e8;
            color: #4CAF50;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e0e0e0;
            padding: 10px 0;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            text-align: center;
            color: #999;
            text-decoration: none;
            padding: 5px;
            transition: color 0.3s ease;
        }
        
        .nav-item.active {
            color: #4CAF50;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 2px;
            display: block;
        }
        
        .nav-item span {
            font-size: 12px;
        }
        
        .content {
            padding-bottom: 80px;
        }
    </style>
</head>
<body>
    <div class="status-bar">
        <span>9:41</span>
        <span>
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </span>
    </div>
    
    <div class="content">
        <div class="header">
            <div class="user-greeting">
                <div class="user-info">
                    <h3>早上好，张先生</h3>
                    <p>今天也要关注健康哦</p>
                </div>
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
            </div>
            <input type="text" class="search-box" placeholder="搜索医生、科室、疾病...">
        </div>
        
        <div class="quick-actions">
            <div class="action-grid">
                <a href="#" class="action-card">
                    <div class="action-icon consult">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="action-title">在线咨询</div>
                    <div class="action-subtitle">专家实时解答</div>
                </a>
                
                <a href="#" class="action-card">
                    <div class="action-icon appointment">
                        <i class="fas fa-calendar-plus"></i>
                    </div>
                    <div class="action-title">预约挂号</div>
                    <div class="action-subtitle">快速预约专家</div>
                </a>
                
                <a href="#" class="action-card">
                    <div class="action-icon health">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="action-title">健康档案</div>
                    <div class="action-subtitle">管理健康数据</div>
                </a>
                
                <a href="#" class="action-card">
                    <div class="action-icon emergency">
                        <i class="fas fa-ambulance"></i>
                    </div>
                    <div class="action-title">急诊服务</div>
                    <div class="action-subtitle">紧急医疗救助</div>
                </a>
            </div>
            
            <div class="section-title">
                <i class="fas fa-star"></i>
                推荐专家
            </div>
            
            <div class="expert-card">
                <div class="expert-avatar">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="expert-info">
                    <div class="expert-name">李主任医师</div>
                    <div class="expert-specialty">心血管内科 · 三甲医院</div>
                    <div class="expert-rating">
                        <i class="fas fa-star"></i> 4.9分 · 1000+好评
                    </div>
                </div>
                <div class="expert-status">在线</div>
            </div>
            
            <div class="expert-card">
                <div class="expert-avatar">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="expert-info">
                    <div class="expert-name">王教授</div>
                    <div class="expert-specialty">神经内科 · 知名专家</div>
                    <div class="expert-rating">
                        <i class="fas fa-star"></i> 4.8分 · 800+好评
                    </div>
                </div>
                <div class="expert-status">在线</div>
            </div>
        </div>
    </div>
    
    <div class="bottom-nav">
        <a href="#" class="nav-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="experts.html" class="nav-item">
            <i class="fas fa-user-md"></i>
            <span>专家</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-calendar"></i>
            <span>预约</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html>
