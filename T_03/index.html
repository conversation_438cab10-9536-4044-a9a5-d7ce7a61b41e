<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医疗专家诊断订阅 App 原型展示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px 10px;
        }
        
        .prototype-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .prototype-title {
            text-align: center;
            color: #2d5a2d;
            font-weight: 700;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .phone-frame {
            width: 280px;
            height: 600px;
            background: #000;
            border-radius: 35px;
            padding: 8px;
            margin: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: relative;
            display: inline-block;
            vertical-align: top;
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 27px;
            overflow: hidden;
            position: relative;
        }
        
        .page-title {
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            color: #2d5a2d;
            margin: 10px 0;
            padding: 0 10px;
        }
        
        .prototype-grid {
            text-align: center;
            line-height: 0;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 27px;
        }
        
        .description {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <h1 class="prototype-title">
            <i class="fas fa-mobile-alt text-success me-2"></i>
            医疗专家诊断订阅 App 原型
        </h1>
        
        <div class="description">
            <h3 class="text-success mb-3">
                <i class="fas fa-stethoscope me-2"></i>
                产品概述
            </h3>
            <p class="mb-3">这是一款专为患者设计的医疗专家诊断订阅应用，旨在为用户提供便捷、专业的在线医疗咨询服务。通过订阅模式，患者可以随时获得专业医生的诊断建议和健康指导。</p>
            
            <div class="feature-list">
                <div class="feature-item">
                    <i class="fas fa-user-md mb-2"></i><br>
                    专业医生团队
                </div>
                <div class="feature-item">
                    <i class="fas fa-calendar-check mb-2"></i><br>
                    便捷预约系统
                </div>
                <div class="feature-item">
                    <i class="fas fa-shield-alt mb-2"></i><br>
                    安全隐私保护
                </div>
                <div class="feature-item">
                    <i class="fas fa-clock mb-2"></i><br>
                    24/7在线服务
                </div>
            </div>
        </div>
        
        <div class="prototype-grid">
            <div class="phone-frame">
                <div class="phone-screen">
                    <iframe src="register.html"></iframe>
                </div>
                <div class="page-title">注册页面</div>
            </div>
            
            <div class="phone-frame">
                <div class="phone-screen">
                    <iframe src="login.html"></iframe>
                </div>
                <div class="page-title">登录页面</div>
            </div>
            
            <div class="phone-frame">
                <div class="phone-screen">
                    <iframe src="home.html"></iframe>
                </div>
                <div class="page-title">主页</div>
            </div>
            
            <div class="phone-frame">
                <div class="phone-screen">
                    <iframe src="experts.html"></iframe>
                </div>
                <div class="page-title">专家列表</div>
            </div>
            
            <div class="phone-frame">
                <div class="phone-screen">
                    <iframe src="expert-detail.html"></iframe>
                </div>
                <div class="page-title">专家详情</div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
