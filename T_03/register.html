<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 医疗专家诊断</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            height: 100vh;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .container-fluid {
            height: calc(100vh - 44px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .register-card {
            background: white;
            border-radius: 20px;
            padding: 30px 25px;
            width: 100%;
            max-width: 320px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 36px;
        }
        
        .app-title {
            font-size: 24px;
            font-weight: 700;
            color: #2d5a2d;
            margin-bottom: 5px;
        }
        
        .app-subtitle {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-control {
            border: 2px solid #e8f5e8;
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e8f5e8;
            border-right: none;
            border-radius: 12px 0 0 12px;
            color: #4CAF50;
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 0 12px 12px 0;
        }
        
        .btn-register {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            margin-top: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .login-link {
            text-align: center;
            margin-top: 20px;
            color: #666;
        }
        
        .login-link a {
            color: #4CAF50;
            text-decoration: none;
            font-weight: 600;
        }
        
        .terms-text {
            font-size: 12px;
            color: #999;
            text-align: center;
            margin-top: 15px;
            line-height: 1.4;
        }
        
        .terms-text a {
            color: #4CAF50;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="status-bar">
        <span>9:41</span>
        <span>
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </span>
    </div>
    
    <div class="container-fluid">
        <div class="register-card">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="app-title">医疗专家</div>
                <div class="app-subtitle">专业诊断，贴心服务</div>
            </div>
            
            <form>
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" class="form-control" placeholder="请输入姓名" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-mobile-alt"></i>
                        </span>
                        <input type="tel" class="form-control" placeholder="请输入手机号" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" class="form-control" placeholder="请设置密码" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-shield-alt"></i>
                        </span>
                        <input type="text" class="form-control" placeholder="请输入验证码" required>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-register">
                    <i class="fas fa-user-plus me-2"></i>
                    立即注册
                </button>
                
                <div class="terms-text">
                    注册即表示同意 <a href="#">《用户协议》</a> 和 <a href="#">《隐私政策》</a>
                </div>
                
                <div class="login-link">
                    已有账号？<a href="login.html">立即登录</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
