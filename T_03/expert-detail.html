<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家详情 - 医疗专家诊断</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            overflow-x: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: #4CAF50;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .back-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        
        .header-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .expert-profile {
            background: white;
            padding: 25px 20px;
            margin-bottom: 15px;
        }
        
        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .expert-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            margin-right: 20px;
        }
        
        .expert-info {
            flex: 1;
        }
        
        .expert-name {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .expert-title {
            color: #4CAF50;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .expert-hospital {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .expert-status {
            background: #e8f5e8;
            color: #4CAF50;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            text-align: center;
            background: #f8f9fa;
            padding: 15px 10px;
            border-radius: 12px;
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: 600;
            color: #4CAF50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .section {
            background: white;
            margin-bottom: 15px;
            padding: 20px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 8px;
            color: #4CAF50;
        }
        
        .specialty-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .specialty-tag {
            background: #f0f8f0;
            color: #4CAF50;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .experience-text {
            color: #666;
            line-height: 1.6;
            font-size: 14px;
        }
        
        .review-item {
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        
        .review-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .reviewer-name {
            font-weight: 500;
            color: #333;
        }
        
        .review-rating {
            color: #4CAF50;
            font-size: 14px;
        }
        
        .review-text {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .schedule-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 8px;
        }
        
        .schedule-day {
            text-align: center;
            padding: 10px 5px;
            border-radius: 8px;
            font-size: 12px;
        }
        
        .schedule-day.available {
            background: #e8f5e8;
            color: #4CAF50;
        }
        
        .schedule-day.busy {
            background: #f5f5f5;
            color: #999;
        }
        
        .schedule-day.selected {
            background: #4CAF50;
            color: white;
        }
        
        .bottom-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 15px 20px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 15px;
        }
        
        .action-btn {
            flex: 1;
            padding: 15px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            border: none;
            transition: all 0.3s ease;
        }
        
        .btn-consult {
            background: #4CAF50;
            color: white;
        }
        
        .btn-consult:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .btn-appointment {
            background: #f8f9fa;
            color: #4CAF50;
            border: 2px solid #4CAF50;
        }
        
        .btn-appointment:hover {
            background: #4CAF50;
            color: white;
        }
        
        .content {
            padding-bottom: 100px;
        }
    </style>
</head>
<body>
    <div class="status-bar">
        <span>9:41</span>
        <span>
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </span>
    </div>
    
    <div class="header">
        <div class="header-left">
            <button class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1 class="header-title">专家详情</h1>
        </div>
        <div class="header-actions">
            <button class="header-btn">
                <i class="fas fa-heart"></i>
            </button>
            <button class="header-btn">
                <i class="fas fa-share"></i>
            </button>
        </div>
    </div>
    
    <div class="content">
        <div class="expert-profile">
            <div class="profile-header">
                <div class="expert-avatar">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="expert-info">
                    <div class="expert-name">李建华</div>
                    <div class="expert-title">主任医师 · 教授</div>
                    <div class="expert-hospital">北京协和医院 心血管内科</div>
                    <div class="expert-status">在线</div>
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">4.9</div>
                    <div class="stat-label">综合评分</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1200+</div>
                    <div class="stat-label">患者好评</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">25年</div>
                    <div class="stat-label">从医经验</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">好评率</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">
                <i class="fas fa-stethoscope"></i>
                擅长领域
            </div>
            <div class="specialty-tags">
                <span class="specialty-tag">冠心病诊治</span>
                <span class="specialty-tag">高血压管理</span>
                <span class="specialty-tag">心律失常</span>
                <span class="specialty-tag">心力衰竭</span>
                <span class="specialty-tag">心脏介入</span>
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">
                <i class="fas fa-graduation-cap"></i>
                专业经历
            </div>
            <div class="experience-text">
                北京协和医学院博士毕业，从事心血管内科临床工作25年。曾在美国梅奥诊所进修学习，擅长冠心病、高血压、心律失常等疾病的诊断和治疗。发表SCI论文50余篇，主持国家自然科学基金项目3项。
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">
                <i class="fas fa-star"></i>
                患者评价
            </div>
            
            <div class="review-item">
                <div class="review-header">
                    <span class="reviewer-name">张**</span>
                    <div class="review-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <div class="review-text">
                    李医生非常专业，诊断准确，治疗方案很有效果。态度也很好，会耐心解答问题。
                </div>
            </div>
            
            <div class="review-item">
                <div class="review-header">
                    <span class="reviewer-name">王**</span>
                    <div class="review-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <div class="review-text">
                    经验丰富的好医生，对病情分析很透彻，给出的建议很实用。
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">
                <i class="fas fa-calendar-alt"></i>
                本周排班
            </div>
            <div class="schedule-grid">
                <div class="schedule-day available">
                    <div>周一</div>
                    <div>可约</div>
                </div>
                <div class="schedule-day busy">
                    <div>周二</div>
                    <div>已满</div>
                </div>
                <div class="schedule-day available">
                    <div>周三</div>
                    <div>可约</div>
                </div>
                <div class="schedule-day selected">
                    <div>周四</div>
                    <div>今天</div>
                </div>
                <div class="schedule-day available">
                    <div>周五</div>
                    <div>可约</div>
                </div>
                <div class="schedule-day busy">
                    <div>周六</div>
                    <div>休息</div>
                </div>
                <div class="schedule-day busy">
                    <div>周日</div>
                    <div>休息</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="bottom-actions">
        <button class="action-btn btn-consult">
            <i class="fas fa-comments me-2"></i>
            立即咨询 ¥50
        </button>
        <button class="action-btn btn-appointment">
            <i class="fas fa-calendar-plus me-2"></i>
            预约挂号
        </button>
    </div>
</body>
</html>
