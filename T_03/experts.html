<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家列表 - 医疗专家诊断</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            overflow-x: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: #4CAF50;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        
        .header-actions {
            display: flex;
            gap: 15px;
        }
        
        .header-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .filter-section {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .filter-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .filter-tab {
            background: #f8f9fa;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active {
            background: #4CAF50;
            color: white;
        }
        
        .search-filter {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 10px 15px;
            font-size: 14px;
        }
        
        .filter-btn {
            background: #4CAF50;
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 14px;
        }
        
        .experts-list {
            padding: 20px;
            padding-bottom: 100px;
        }
        
        .expert-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .expert-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .expert-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .expert-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
            margin-right: 15px;
        }
        
        .expert-basic-info {
            flex: 1;
        }
        
        .expert-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .expert-title {
            color: #4CAF50;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .expert-hospital {
            color: #666;
            font-size: 13px;
        }
        
        .expert-status {
            background: #e8f5e8;
            color: #4CAF50;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .expert-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px 0;
            border-top: 1px solid #f0f0f0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .stat-item {
            text-align: center;
            flex: 1;
        }
        
        .stat-number {
            font-size: 16px;
            font-weight: 600;
            color: #4CAF50;
            margin-bottom: 2px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .expert-specialties {
            margin-bottom: 15px;
        }
        
        .specialty-tag {
            display: inline-block;
            background: #f0f8f0;
            color: #4CAF50;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 8px;
            margin-bottom: 5px;
        }
        
        .expert-actions {
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            border: none;
            transition: all 0.3s ease;
        }
        
        .btn-consult {
            background: #4CAF50;
            color: white;
        }
        
        .btn-consult:hover {
            background: #45a049;
        }
        
        .btn-appointment {
            background: #f8f9fa;
            color: #4CAF50;
            border: 1px solid #4CAF50;
        }
        
        .btn-appointment:hover {
            background: #4CAF50;
            color: white;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e0e0e0;
            padding: 10px 0;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            text-align: center;
            color: #999;
            text-decoration: none;
            padding: 5px;
            transition: color 0.3s ease;
        }
        
        .nav-item.active {
            color: #4CAF50;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 2px;
            display: block;
        }
        
        .nav-item span {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="status-bar">
        <span>9:41</span>
        <span>
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </span>
    </div>
    
    <div class="header">
        <h1 class="header-title">专家医生</h1>
        <div class="header-actions">
            <button class="header-btn">
                <i class="fas fa-search"></i>
            </button>
            <button class="header-btn">
                <i class="fas fa-filter"></i>
            </button>
        </div>
    </div>
    
    <div class="filter-section">
        <div class="filter-tabs">
            <button class="filter-tab active">全部</button>
            <button class="filter-tab">心血管</button>
            <button class="filter-tab">神经科</button>
            <button class="filter-tab">消化科</button>
            <button class="filter-tab">呼吸科</button>
        </div>
        
        <div class="search-filter">
            <input type="text" class="search-input" placeholder="搜索医生姓名或专业">
            <button class="filter-btn">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
    
    <div class="experts-list">
        <div class="expert-card">
            <div class="expert-header">
                <div class="expert-avatar">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="expert-basic-info">
                    <div class="expert-name">李建华 主任医师</div>
                    <div class="expert-title">心血管内科专家</div>
                    <div class="expert-hospital">北京协和医院</div>
                </div>
                <div class="expert-status">在线</div>
            </div>
            
            <div class="expert-stats">
                <div class="stat-item">
                    <div class="stat-number">4.9</div>
                    <div class="stat-label">评分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">1200+</div>
                    <div class="stat-label">好评</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">25年</div>
                    <div class="stat-label">经验</div>
                </div>
            </div>
            
            <div class="expert-specialties">
                <span class="specialty-tag">冠心病</span>
                <span class="specialty-tag">高血压</span>
                <span class="specialty-tag">心律失常</span>
            </div>
            
            <div class="expert-actions">
                <button class="action-btn btn-consult">
                    <i class="fas fa-comments me-1"></i>
                    立即咨询
                </button>
                <button class="action-btn btn-appointment">
                    <i class="fas fa-calendar me-1"></i>
                    预约挂号
                </button>
            </div>
        </div>
        
        <div class="expert-card">
            <div class="expert-header">
                <div class="expert-avatar">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="expert-basic-info">
                    <div class="expert-name">王美丽 教授</div>
                    <div class="expert-title">神经内科专家</div>
                    <div class="expert-hospital">清华长庚医院</div>
                </div>
                <div class="expert-status">在线</div>
            </div>
            
            <div class="expert-stats">
                <div class="stat-item">
                    <div class="stat-number">4.8</div>
                    <div class="stat-label">评分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">980+</div>
                    <div class="stat-label">好评</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">20年</div>
                    <div class="stat-label">经验</div>
                </div>
            </div>
            
            <div class="expert-specialties">
                <span class="specialty-tag">脑卒中</span>
                <span class="specialty-tag">癫痫</span>
                <span class="specialty-tag">帕金森</span>
            </div>
            
            <div class="expert-actions">
                <button class="action-btn btn-consult">
                    <i class="fas fa-comments me-1"></i>
                    立即咨询
                </button>
                <button class="action-btn btn-appointment">
                    <i class="fas fa-calendar me-1"></i>
                    预约挂号
                </button>
            </div>
        </div>
        
        <div class="expert-card">
            <div class="expert-header">
                <div class="expert-avatar">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="expert-basic-info">
                    <div class="expert-name">张伟 副主任医师</div>
                    <div class="expert-title">消化内科专家</div>
                    <div class="expert-hospital">北京大学第一医院</div>
                </div>
                <div class="expert-status">忙碌</div>
            </div>
            
            <div class="expert-stats">
                <div class="stat-item">
                    <div class="stat-number">4.7</div>
                    <div class="stat-label">评分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">750+</div>
                    <div class="stat-label">好评</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">15年</div>
                    <div class="stat-label">经验</div>
                </div>
            </div>
            
            <div class="expert-specialties">
                <span class="specialty-tag">胃炎</span>
                <span class="specialty-tag">肠炎</span>
                <span class="specialty-tag">肝病</span>
            </div>
            
            <div class="expert-actions">
                <button class="action-btn btn-consult">
                    <i class="fas fa-comments me-1"></i>
                    立即咨询
                </button>
                <button class="action-btn btn-appointment">
                    <i class="fas fa-calendar me-1"></i>
                    预约挂号
                </button>
            </div>
        </div>
    </div>
    
    <div class="bottom-nav">
        <a href="home.html" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="#" class="nav-item active">
            <i class="fas fa-user-md"></i>
            <span>专家</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-calendar"></i>
            <span>预约</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html>
