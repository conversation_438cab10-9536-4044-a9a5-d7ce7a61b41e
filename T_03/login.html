<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 医疗专家诊断</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            height: 100vh;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .container-fluid {
            height: calc(100vh - 44px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            padding: 40px 25px;
            width: 100%;
            max-width: 320px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 36px;
        }
        
        .app-title {
            font-size: 24px;
            font-weight: 700;
            color: #2d5a2d;
            margin-bottom: 5px;
        }
        
        .app-subtitle {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-control {
            border: 2px solid #e8f5e8;
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e8f5e8;
            border-right: none;
            border-radius: 12px 0 0 12px;
            color: #4CAF50;
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 0 12px 12px 0;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            margin-top: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .forgot-password {
            text-align: right;
            margin-top: 10px;
        }
        
        .forgot-password a {
            color: #4CAF50;
            text-decoration: none;
            font-size: 14px;
        }
        
        .register-link {
            text-align: center;
            margin-top: 30px;
            color: #666;
        }
        
        .register-link a {
            color: #4CAF50;
            text-decoration: none;
            font-weight: 600;
        }
        
        .social-login {
            margin-top: 30px;
            text-align: center;
        }
        
        .social-title {
            color: #999;
            font-size: 14px;
            margin-bottom: 20px;
            position: relative;
        }
        
        .social-title::before,
        .social-title::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 30%;
            height: 1px;
            background: #ddd;
        }
        
        .social-title::before {
            left: 0;
        }
        
        .social-title::after {
            right: 0;
        }
        
        .social-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        
        .social-btn {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            border: 2px solid #e8f5e8;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .social-btn:hover {
            border-color: #4CAF50;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="status-bar">
        <span>9:41</span>
        <span>
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </span>
    </div>
    
    <div class="container-fluid">
        <div class="login-card">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="app-title">欢迎回来</div>
                <div class="app-subtitle">登录您的医疗专家账户</div>
            </div>
            
            <form>
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-mobile-alt"></i>
                        </span>
                        <input type="tel" class="form-control" placeholder="请输入手机号" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" class="form-control" placeholder="请输入密码" required>
                    </div>
                </div>
                
                <div class="forgot-password">
                    <a href="#">忘记密码？</a>
                </div>
                
                <button type="submit" class="btn btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    立即登录
                </button>
                
                <div class="social-login">
                    <div class="social-title">其他登录方式</div>
                    <div class="social-buttons">
                        <button type="button" class="social-btn">
                            <i class="fab fa-weixin"></i>
                        </button>
                        <button type="button" class="social-btn">
                            <i class="fab fa-apple"></i>
                        </button>
                    </div>
                </div>
                
                <div class="register-link">
                    还没有账号？<a href="register.html">立即注册</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
